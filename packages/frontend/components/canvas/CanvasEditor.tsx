"use client";

import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Slider } from '@/components/ui/slider';
import { ZoomIn, ZoomOut, RotateCw, Move, Square, Type, Download, Save } from 'lucide-react';
import { ProjectPageResponse, TextRegionResponse, TextRegionType } from '@/lib/types';
import { apiClient } from '@/lib/api';

interface CanvasEditorProps {
  page?: ProjectPageResponse;
  textRegions?: TextRegionResponse[];
  onTextRegionCreate?: (region: Partial<TextRegionResponse>) => void;
  onTextRegionUpdate?: (regionId: string, updates: Partial<TextRegionResponse>) => void;
  onTextRegionDelete?: (regionId: string) => void;
  onSave?: () => void;
}

export type DrawMode = 'select' | 'text' | 'rect';

interface TextRegion {
  id: string;
  x: number;
  y: number;
  width: number;
  height: number;
  text?: string;
  selected?: boolean;
}

export default function CanvasEditor({
  page,
  textRegions = [],
  onTextRegionCreate,
  onTextRegionUpdate,
  onTextRegionDelete,
  onSave,
}: CanvasEditorProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [zoom, setZoom] = useState(1);
  const [drawMode, setDrawMode] = useState<DrawMode>('select');
  const [isDrawing, setIsDrawing] = useState(false);
  const [backgroundImage, setBackgroundImage] = useState<HTMLImageElement | null>(null);
  const [canvasRegions, setCanvasRegions] = useState<TextRegion[]>([]);
  const [selectedRegionId, setSelectedRegionId] = useState<string | null>(null);
  const [drawingRect, setDrawingRect] = useState<{ startX: number; startY: number; currentX: number; currentY: number } | null>(null);

  // Convert backend text regions to canvas regions
  useEffect(() => {
    if (!backgroundImage) return;

    const regions: TextRegion[] = textRegions.map(region => ({
      id: region.id,
      x: region.x * backgroundImage.naturalWidth,
      y: region.y * backgroundImage.naturalHeight,
      width: region.width * backgroundImage.naturalWidth,
      height: region.height * backgroundImage.naturalHeight,
      text: region.translated_text || region.original_text,
    }));

    setCanvasRegions(regions);
  }, [textRegions, backgroundImage]);

  // Load background image when page changes
  useEffect(() => {
    if (!page) return;

    const imageUrl = apiClient.getPageImageUrl(page.id);
    const img = new Image();
    img.crossOrigin = 'anonymous';
    img.onload = () => {
      setBackgroundImage(img);
      redrawCanvas();
    };
    img.onerror = (error) => {
      console.error('Failed to load image:', error);
      console.log('Image URL:', imageUrl);
    };
    img.src = imageUrl;
  }, [page]);

  // Redraw canvas
  const redrawCanvas = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background image
    if (backgroundImage) {
      const scale = Math.min(canvas.width / backgroundImage.naturalWidth, canvas.height / backgroundImage.naturalHeight) * zoom;
      const scaledWidth = backgroundImage.naturalWidth * scale;
      const scaledHeight = backgroundImage.naturalHeight * scale;
      const x = (canvas.width - scaledWidth) / 2;
      const y = (canvas.height - scaledHeight) / 2;

      ctx.drawImage(backgroundImage, x, y, scaledWidth, scaledHeight);
    }

    // Draw text regions
    canvasRegions.forEach(region => {
      const scale = backgroundImage ? Math.min(canvas.width / backgroundImage.naturalWidth, canvas.height / backgroundImage.naturalHeight) * zoom : 1;
      const x = region.x * scale + (canvas.width - (backgroundImage?.naturalWidth || 0) * scale) / 2;
      const y = region.y * scale + (canvas.height - (backgroundImage?.naturalHeight || 0) * scale) / 2;
      const width = region.width * scale;
      const height = region.height * scale;

      // Draw region rectangle
      ctx.strokeStyle = region.selected ? '#3b82f6' : '#fbbf24';
      ctx.fillStyle = region.selected ? 'rgba(59, 130, 246, 0.2)' : 'rgba(251, 191, 36, 0.2)';
      ctx.lineWidth = 2;
      ctx.fillRect(x, y, width, height);
      ctx.strokeRect(x, y, width, height);

      // Draw text if available
      if (region.text) {
        ctx.fillStyle = '#000000';
        ctx.font = '14px Arial';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(region.text, x + width / 2, y + height / 2);
      }
    });

    // Draw current drawing rectangle
    if (drawingRect && drawMode === 'rect') {
      ctx.strokeStyle = '#ef4444';
      ctx.fillStyle = 'rgba(239, 68, 68, 0.2)';
      ctx.lineWidth = 2;
      const x = Math.min(drawingRect.startX, drawingRect.currentX);
      const y = Math.min(drawingRect.startY, drawingRect.currentY);
      const width = Math.abs(drawingRect.currentX - drawingRect.startX);
      const height = Math.abs(drawingRect.currentY - drawingRect.startY);
      ctx.fillRect(x, y, width, height);
      ctx.strokeRect(x, y, width, height);
    }
  }, [backgroundImage, canvasRegions, zoom, drawingRect, drawMode]);

  // Redraw when dependencies change
  useEffect(() => {
    redrawCanvas();
  }, [redrawCanvas]);

  // Mouse event handlers
  const handleMouseDown = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (drawMode !== 'rect') return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    setDrawingRect({ startX: x, startY: y, currentX: x, currentY: y });
    setIsDrawing(true);
  }, [drawMode]);

  const handleMouseMove = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    if (isDrawing && drawingRect) {
      setDrawingRect(prev => prev ? { ...prev, currentX: x, currentY: y } : null);
    }
  }, [isDrawing, drawingRect]);

  const handleMouseUp = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (!isDrawing || !drawingRect || !backgroundImage) return;

    const canvas = canvasRef.current;
    if (!canvas) return;

    const scale = Math.min(canvas.width / backgroundImage.naturalWidth, canvas.height / backgroundImage.naturalHeight) * zoom;
    const offsetX = (canvas.width - backgroundImage.naturalWidth * scale) / 2;
    const offsetY = (canvas.height - backgroundImage.naturalHeight * scale) / 2;

    // Convert canvas coordinates to normalized coordinates
    const x = Math.min(drawingRect.startX, drawingRect.currentX);
    const y = Math.min(drawingRect.startY, drawingRect.currentY);
    const width = Math.abs(drawingRect.currentX - drawingRect.startX);
    const height = Math.abs(drawingRect.currentY - drawingRect.startY);

    // Convert to image coordinates
    const normalizedX = (x - offsetX) / scale / backgroundImage.naturalWidth;
    const normalizedY = (y - offsetY) / scale / backgroundImage.naturalHeight;
    const normalizedWidth = width / scale / backgroundImage.naturalWidth;
    const normalizedHeight = height / scale / backgroundImage.naturalHeight;

    // Only create if region has meaningful size
    if (normalizedWidth > 0.01 && normalizedHeight > 0.01) {
      const newRegion = {
        x: Math.max(0, Math.min(1, normalizedX)),
        y: Math.max(0, Math.min(1, normalizedY)),
        width: Math.min(1 - normalizedX, normalizedWidth),
        height: Math.min(1 - normalizedY, normalizedHeight),
        region_type: TextRegionType.SPEECH_BUBBLE,
        page_id: page?.id || '',
      };
      onTextRegionCreate?.(newRegion);
    }

    setDrawingRect(null);
    setIsDrawing(false);
    setDrawMode('select');
  }, [isDrawing, drawingRect, backgroundImage, zoom, page, onTextRegionCreate]);

  const handleRegionClick = useCallback((regionId: string) => {
    setSelectedRegionId(regionId);
    setCanvasRegions(prev => prev.map(r => ({ ...r, selected: r.id === regionId })));
  }, []);

  // Canvas click handler for region selection
  const handleCanvasClick = useCallback((e: React.MouseEvent<HTMLCanvasElement>) => {
    if (drawMode !== 'select') return;

    const canvas = canvasRef.current;
    if (!canvas || !backgroundImage) return;

    const rect = canvas.getBoundingClientRect();
    const x = e.clientX - rect.left;
    const y = e.clientY - rect.top;

    const scale = Math.min(canvas.width / backgroundImage.naturalWidth, canvas.height / backgroundImage.naturalHeight) * zoom;
    const offsetX = (canvas.width - backgroundImage.naturalWidth * scale) / 2;
    const offsetY = (canvas.height - backgroundImage.naturalHeight * scale) / 2;

    // Check if click is within any region
    for (const region of canvasRegions) {
      const regionX = region.x * scale + offsetX;
      const regionY = region.y * scale + offsetY;
      const regionWidth = region.width * scale;
      const regionHeight = region.height * scale;

      if (x >= regionX && x <= regionX + regionWidth && y >= regionY && y <= regionY + regionHeight) {
        handleRegionClick(region.id);
        return;
      }
    }

    // No region clicked, deselect all
    setSelectedRegionId(null);
    setCanvasRegions(prev => prev.map(r => ({ ...r, selected: false })));
  }, [drawMode, backgroundImage, zoom, canvasRegions, handleRegionClick]);

  // Zoom controls
  const handleZoomIn = useCallback(() => {
    const newZoom = Math.min(zoom * 1.2, 20);
    setZoom(newZoom);
  }, [zoom]);

  const handleZoomOut = useCallback(() => {
    const newZoom = Math.max(zoom / 1.2, 0.01);
    setZoom(newZoom);
  }, [zoom]);

  const handleZoomReset = useCallback(() => {
    setZoom(1);
  }, []);

  // Export canvas as image
  const handleExport = useCallback(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const dataURL = canvas.toDataURL('image/png', 0.8);

    const link = document.createElement('a');
    link.download = `manga-page-${page?.page_number || 'export'}.png`;
    link.href = dataURL;
    link.click();
  }, [page]);

  return (
    <div className="flex flex-col h-full">
      {/* Toolbar */}
      <div className="flex items-center gap-2 p-4 border-b bg-white">
        <div className="flex items-center gap-2">
          <Button
            variant={drawMode === 'select' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDrawMode('select')}
          >
            <Move className="w-4 h-4" />
            Select
          </Button>
          <Button
            variant={drawMode === 'rect' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDrawMode('rect')}
          >
            <Square className="w-4 h-4" />
            Text Region
          </Button>
          <Button
            variant={drawMode === 'text' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDrawMode('text')}
          >
            <Type className="w-4 h-4" />
            Text
          </Button>
        </div>

        <div className="w-px h-6 bg-gray-300" />

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleZoomOut}>
            <ZoomOut className="w-4 h-4" />
          </Button>
          <span className="text-sm font-mono w-16 text-center">
            {Math.round(zoom * 100)}%
          </span>
          <Button variant="outline" size="sm" onClick={handleZoomIn}>
            <ZoomIn className="w-4 h-4" />
          </Button>
          <Button variant="outline" size="sm" onClick={handleZoomReset}>
            Reset
          </Button>
        </div>

        <div className="w-px h-6 bg-gray-300" />

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleExport}>
            <Download className="w-4 h-4" />
            Export
          </Button>
          <Button variant="default" size="sm" onClick={onSave}>
            <Save className="w-4 h-4" />
            Save
          </Button>
        </div>
      </div>

      {/* Canvas Container */}
      <div className="flex-1 overflow-hidden bg-gray-100" ref={containerRef}>
        <div className="w-full h-full flex items-center justify-center">
          <canvas
            ref={canvasRef}
            width={800}
            height={600}
            className="border border-gray-300 bg-white shadow-lg cursor-crosshair"
            onMouseDown={handleMouseDown}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onClick={handleCanvasClick}
            style={{ cursor: drawMode === 'rect' ? 'crosshair' : 'default' }}
          />
        </div>
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between p-2 border-t bg-gray-50 text-sm text-gray-600">
        <div>
          {page ? `Page ${page.page_number} - ${page.original_filename}` : 'No page loaded'}
        </div>
        <div>
          {textRegions.length} text regions
        </div>
      </div>
    </div>
  );
}
