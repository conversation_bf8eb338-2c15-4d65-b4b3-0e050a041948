#!/usr/bin/env python3
"""
Test script to simulate the exact API call that's failing.
"""
import asyncio
import sys
import json
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from src.main import app
from src.projects.service import ProjectService
from src.projects.schemas import ProjectCreate, ProjectPageCreate, TextRegionCreate
from src.constants import TextRegionType


async def setup_test_data():
    """Set up test data for API testing."""
    # Create in-memory SQLite database for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    
    # Import and create tables
    from src.models import BaseModel
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    
    # Create session
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        service = ProjectService(session)
        
        # Create test project
        project_data = ProjectCreate(
            name="Test Project",
            description="Test Description",
            source_language="japanese",
            target_language="english"
        )
        project = await service.create_project(project_data)
        
        # Create test page
        page_data = ProjectPageCreate(
            page_number=1,
            original_filename="test.jpg"
        )
        page = await service.create_project_page(
            project.id, page_data, "/test/test.jpg", 1000, 800, 600
        )
        
        # Create test text region
        region_data = TextRegionCreate(
            region_type=TextRegionType.SPEECH_BUBBLE,
            x=0.1, y=0.2, width=0.3, height=0.4,
            original_text="Original text", confidence_score=0.95
        )
        text_region = await service.create_text_region(page.id, region_data)
        
        return project.id, page.id, text_region.id


def test_text_region_update_api():
    """Test the actual API endpoint that's failing."""
    print("🧪 Testing Text Region Update API Endpoint...")
    
    # Note: This test won't work with async database setup in TestClient
    # But we can test the request/response structure
    
    client = TestClient(app)
    
    # Test various payloads that might be sent from frontend
    test_payloads = [
        {
            "name": "Basic text update",
            "payload": {
                "original_text": "Updated original text",
                "translated_text": "Updated translated text"
            }
        },
        {
            "name": "Position update",
            "payload": {
                "x": 0.5,
                "y": 0.6,
                "width": 0.2,
                "height": 0.3
            }
        },
        {
            "name": "Font styling update",
            "payload": {
                "font_family": "Arial",
                "font_size": 14,
                "font_color": "#000000",
                "background_color": "#FFFFFF"
            }
        },
        {
            "name": "Empty payload",
            "payload": {}
        },
        {
            "name": "Partial coordinate update",
            "payload": {
                "x": 0.7
            }
        },
        {
            "name": "Frontend-style payload with extra fields",
            "payload": {
                "x": 0.5,
                "y": 0.6,
                "width": 0.2,
                "height": 0.3,
                "original_text": "Text from frontend",
                "id": "should-be-ignored",
                "created_at": "should-be-ignored",
                "updated_at": "should-be-ignored"
            }
        },
        {
            "name": "Invalid coordinate values",
            "payload": {
                "x": -0.1,
                "y": 1.5
            },
            "should_fail": True
        },
        {
            "name": "Invalid color format",
            "payload": {
                "font_color": "invalid-color"
            },
            "should_fail": True
        },
        {
            "name": "String numbers (common frontend issue)",
            "payload": {
                "x": "0.5",
                "y": "0.6",
                "font_size": "14"
            }
        },
        {
            "name": "Boolean values as strings",
            "payload": {
                "x": 0.5,
                "some_boolean_field": "true"  # This might cause issues
            }
        }
    ]
    
    # Use dummy IDs for testing request structure
    project_id = "test-project-id"
    page_id = "test-page-id"
    region_id = "test-region-id"
    
    for test_case in test_payloads:
        print(f"\n🧪 Testing: {test_case['name']}")
        should_fail = test_case.get("should_fail", False)
        
        try:
            # Test the request structure (this will fail due to missing data, but we can see validation errors)
            response = client.put(
                f"/api/v1/projects/{project_id}/pages/{page_id}/regions/{region_id}",
                json=test_case["payload"]
            )
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 422:
                # Parse validation errors
                error_detail = response.json()
                print(f"  Validation errors: {json.dumps(error_detail, indent=2)}")
                
                if should_fail:
                    print(f"  ✅ Expected validation failure")
                else:
                    print(f"  ❌ Unexpected validation failure")
            elif response.status_code == 404:
                print(f"  ℹ️ Expected 404 (test data doesn't exist)")
            else:
                print(f"  Response: {response.json()}")
                
        except Exception as e:
            print(f"  ❌ Request error: {e}")
    
    print("\n🎉 API endpoint testing completed!")


if __name__ == "__main__":
    test_text_region_update_api()
