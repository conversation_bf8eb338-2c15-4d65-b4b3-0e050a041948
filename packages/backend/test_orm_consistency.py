#!/usr/bin/env python3
"""
Test script to verify ORM consistency across all service methods.
"""
from src.constants import TextRegionType
from src.pagination import PaginationParams
from src.projects.schemas import ProjectCreate, ProjectPageCreate, TextRegionCreate
from src.projects.service import ProjectService
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import asyncio
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def test_orm_consistency():
    """Test that all service methods use consistent ORM patterns."""
    # Create in-memory SQLite database for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)

    # Import and create tables
    from src.models import BaseModel
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)

    # Create session
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        service = ProjectService(session)

        try:
            print("🧪 Testing ORM Consistency...")

            # Create multiple projects for pagination testing
            projects = []
            for i in range(15):
                project_data = ProjectCreate(
                    name=f"Test Project {i+1}",
                    description=f"Test Description {i+1}",
                    source_language="japanese",
                    target_language="english"
                )
                project = await service.create_project(project_data)
                projects.append(project)
                print(f"✅ Created project {i+1}: {project.id}")

            # Test pagination with ORM approach
            print("\n🧪 Testing Paginated Queries...")

            # Test first page
            params = PaginationParams(page=1, limit=10)
            paginated_result = await service.get_projects_paginated(params)

            print(f"✅ Page 1: {len(paginated_result.items)} items")
            print(f"✅ Total: {paginated_result.meta.total} projects")
            print(f"✅ Pages: {paginated_result.meta.pages}")
            print(f"✅ Has next: {paginated_result.meta.has_next}")

            # Test second page
            params = PaginationParams(page=2, limit=10)
            paginated_result = await service.get_projects_paginated(params)

            print(f"✅ Page 2: {len(paginated_result.items)} items")
            print(f"✅ Has prev: {paginated_result.meta.has_prev}")

            # Test project with pages and regions
            print("\n🧪 Testing Aggregation Consistency...")

            test_project = projects[0]

            # Add pages to first project
            for i in range(3):
                page_data = ProjectPageCreate(
                    page_number=i+1,
                    original_filename=f"page_{i+1}.jpg"
                )
                page = await service.create_project_page(
                    test_project.id, page_data, f"/test/page_{i+1}.jpg", 1000, 800, 600
                )
                print(f"✅ Created page {i+1}: {page.id}")

                # Add text regions to each page
                for j in range(2):
                    region_data = TextRegionCreate(
                        region_type=TextRegionType.SPEECH_BUBBLE,
                        x=0.1 + j*0.1, y=0.2 + j*0.1, width=0.3, height=0.4,
                        original_text=f"Text {j+1}", confidence_score=0.95
                    )
                    region = await service.create_text_region(page.id, region_data)
                    print(f"✅ Created region {j+1} on page {i+1}: {region.id}")

            # Test aggregation in single project query
            updated_project = await service.get_project(test_project.id)
            print(f"✅ Project page count: {updated_project.page_count}")

            # Test aggregation in project list (get all to ensure we find our project)
            project_list = await service.get_projects(limit=20)
            first_project = next(
                (p for p in project_list if p.id == test_project.id), None)
            if first_project:
                print(f"✅ Project list page count: {first_project.page_count}")
            else:
                print("⚠️ Test project not found in project list")

            # Test aggregation in paginated results (get all to ensure we find our project)
            paginated_result = await service.get_projects_paginated(PaginationParams(page=1, limit=20))
            first_paginated = next(
                (p for p in paginated_result.items if p.id == test_project.id), None)
            if first_paginated:
                print(f"✅ Paginated page count: {first_paginated.page_count}")
            else:
                print("⚠️ Test project not found in paginated results")

            # Verify consistency
            assert updated_project.page_count == 3
            if first_project:
                assert first_project.page_count == 3
            if first_paginated:
                assert first_paginated.page_count == 3
            print("✅ Page count aggregation consistent across all methods")

            # Test page aggregation
            pages = await service.get_project_pages(test_project.id)
            for page in pages:
                print(
                    f"✅ Page {page.page_number} text region count: {page.text_region_count}")
                assert page.text_region_count == 2

            print("\n🎉 All ORM consistency tests passed!")
            print("✅ Single entity retrieval: scalar_one_or_none()")
            print("✅ Multiple entity retrieval: scalars().all()")
            print("✅ Pagination: paginate_orm()")
            print("✅ Aggregation: separate efficient queries")
            print("✅ Response serialization: model.to_dict()")

            return True

        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    success = asyncio.run(test_orm_consistency())
    sys.exit(0 if success else 1)
