["tests/llm_providers/test_llm_api.py::test_analyze_image", "tests/llm_providers/test_llm_api.py::test_batch_generation_partial_failure", "tests/llm_providers/test_llm_api.py::test_generate_text", "tests/llm_providers/test_llm_api.py::test_generate_text_batch", "tests/llm_providers/test_llm_api.py::test_get_provider_models", "tests/llm_providers/test_llm_api.py::test_get_providers_status", "tests/llm_providers/test_llm_api.py::test_image_validation_error", "tests/llm_providers/test_llm_api.py::test_perform_ocr", "tests/llm_providers/test_llm_api.py::test_provider_not_available", "tests/llm_providers/test_llm_api.py::test_text_generation_error", "tests/llm_providers/test_llm_api.py::test_translate_text", "tests/ocr/test_ocr_api.py::test_create_ocr_job", "tests/ocr/test_ocr_api.py::test_get_ocr_job", "tests/ocr/test_ocr_api.py::test_get_ocr_job_detail", "tests/ocr/test_ocr_api.py::test_get_ocr_results", "tests/ocr/test_ocr_api.py::test_get_ocr_statistics", "tests/ocr/test_ocr_api.py::test_get_page_ocr_jobs", "tests/ocr/test_ocr_api.py::test_ocr_job_not_found", "tests/ocr/test_ocr_api.py::test_process_ocr", "tests/ocr/test_ocr_api.py::test_retry_ocr_job", "tests/projects/test_projects_api.py::test_create_project", "tests/projects/test_projects_api.py::test_create_project_validation_error", "tests/projects/test_projects_api.py::test_delete_project", "tests/projects/test_projects_api.py::test_get_project_by_id", "tests/projects/test_projects_api.py::test_get_project_detail", "tests/projects/test_projects_api.py::test_get_projects_paginated", "tests/projects/test_projects_api.py::test_pagination_limit_validation", "tests/projects/test_projects_api.py::test_pagination_parameters", "tests/projects/test_projects_api.py::test_project_not_found", "tests/projects/test_projects_api.py::test_update_project", "tests/test_pagination.py::TestPaginatedResponse::test_paginated_response_creation", "tests/test_pagination.py::TestPaginatedResponse::test_paginated_response_empty", "tests/test_pagination.py::TestPaginatedResponse::test_paginated_response_last_page", "tests/test_pagination.py::TestPaginationMeta::test_pagination_meta_creation", "tests/test_pagination.py::TestPaginationParams::test_pagination_params_custom", "tests/test_pagination.py::TestPaginationParams::test_pagination_params_defaults", "tests/test_pagination.py::TestPaginationParams::test_pagination_params_validation", "tests/test_pagination.py::TestPaginator::test_paginator_basic", "tests/test_pagination.py::TestPaginator::test_paginator_response", "tests/test_pagination.py::TestPaginator::test_paginator_with_custom_count_query", "tests/test_pagination.py::TestUtilityFunctions::test_create_paginated_response", "tests/test_pagination.py::TestUtilityFunctions::test_get_pagination_info", "tests/test_pagination.py::TestUtilityFunctions::test_get_pagination_info_edge_cases", "tests/translation/test_translation_api.py::test_create_translation_job", "tests/translation/test_translation_api.py::test_create_translation_template", "tests/translation/test_translation_api.py::test_get_translation_alternatives", "tests/translation/test_translation_api.py::test_get_translation_job", "tests/translation/test_translation_api.py::test_get_translation_job_detail", "tests/translation/test_translation_api.py::test_get_translation_statistics", "tests/translation/test_translation_api.py::test_get_translation_templates_paginated", "tests/translation/test_translation_api.py::test_process_translation", "tests/translation/test_translation_api.py::test_select_translation_alternative", "tests/translation/test_translation_api.py::test_translation_job_not_found"]