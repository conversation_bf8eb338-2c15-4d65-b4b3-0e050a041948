"""
Business logic for project management.
"""
from typing import List, Optional

from sqlalchemy import select, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession

from src.pagination import PaginationParams, PaginatedResponse, Paginator

from src.projects.models import Project, ProjectPage, TextRegion
from src.projects.schemas import (
    ProjectCreate, ProjectUpdate, ProjectResponse, ProjectDetailResponse,
    ProjectPageCreate, ProjectPageResponse, ProjectPageDetailResponse,
    TextRegionCreate, TextRegionUpdate, TextRegionResponse
)
from src.projects.exceptions import ProjectNotFound, ProjectPageNotFound, TextRegionNotFound
from src.constants import ProjectStatus, OCRStatus, TranslationStatus


class ProjectService:
    """Service class for project management operations."""

    def __init__(self, session: AsyncSession):
        self.session = session
        self.paginator = Paginator(session)

    async def create_project(self, project_data: ProjectCreate) -> ProjectResponse:
        """Create a new project."""
        # Create ORM model instance - timestamps will be set automatically
        project = Project(
            name=project_data.name,
            description=project_data.description,
            source_language=project_data.source_language,
            target_language=project_data.target_language,
            status=ProjectStatus.DRAFT
        )

        self.session.add(project)
        await self.session.commit()
        await self.session.refresh(project)

        return await self.get_project(project.id)

    async def get_project(self, project_id: str) -> ProjectResponse:
        """Get a project by ID."""
        # Get the project
        query = select(Project).where(Project.id == project_id)
        result = await self.session.execute(query)
        project = result.scalar_one_or_none()

        if not project:
            raise ProjectNotFound(f"Project with ID {project_id} not found")

        # Get page count
        page_count_query = select(func.count(ProjectPage.id)).where(
            ProjectPage.project_id == project_id)
        page_count_result = await self.session.execute(page_count_query)
        page_count = page_count_result.scalar() or 0

        # Create response with aggregated data
        project_dict = project.to_dict()
        project_dict["page_count"] = page_count
        return ProjectResponse(**project_dict)

    async def get_projects(self, limit: int = 10, offset: int = 0) -> List[ProjectResponse]:
        """Get all projects with pagination."""
        # Get projects
        query = select(Project).order_by(
            Project.updated_at.desc()).limit(limit).offset(offset)
        result = await self.session.execute(query)
        projects = result.scalars().all()

        # Get page counts for all projects in one query
        project_ids = [project.id for project in projects]
        if project_ids:
            page_count_query = select(
                ProjectPage.project_id,
                func.count(ProjectPage.id).label("page_count")
            ).where(ProjectPage.project_id.in_(project_ids)).group_by(ProjectPage.project_id)
            page_count_result = await self.session.execute(page_count_query)
            page_counts = {
                row.project_id: row.page_count for row in page_count_result}
        else:
            page_counts = {}

        # Build responses
        responses = []
        for project in projects:
            project_dict = project.to_dict()
            project_dict["page_count"] = page_counts.get(project.id, 0)
            responses.append(ProjectResponse(**project_dict))

        return responses

    async def get_projects_paginated(self, params: PaginationParams) -> PaginatedResponse[ProjectResponse]:
        """Get projects with pagination."""
        query = select(
            Project.id,
            Project.name,
            Project.description,
            Project.status,
            Project.source_language,
            Project.target_language,
            Project.created_at,
            Project.updated_at,
            func.count(ProjectPage.id).label("page_count")
        ).select_from(
            Project.__table__.outerjoin(ProjectPage.__table__)
        ).group_by(
            Project.id
        ).order_by(
            Project.updated_at.desc()
        )

        items, total = await self.paginator.paginate(query, params)
        project_responses = [ProjectResponse(**item) for item in items]

        return PaginatedResponse.create(project_responses, total, params)

    async def update_project(self, project_id: str, project_data: ProjectUpdate) -> ProjectResponse:
        """Update a project."""
        # Check if project exists
        await self.get_project(project_id)

        # Build update data
        update_data = project_data.model_dump(exclude_unset=True)
        if not update_data:
            return await self.get_project(project_id)

        query = update(Project).where(
            Project.id == project_id).values(**update_data)
        await self.session.execute(query)
        await self.session.commit()

        return await self.get_project(project_id)

    async def delete_project(self, project_id: str) -> bool:
        """Delete a project."""
        # Check if project exists
        await self.get_project(project_id)

        query = delete(Project).where(Project.id == project_id)
        await self.session.execute(query)
        await self.session.commit()
        return True

    async def get_project_detail(self, project_id: str) -> ProjectDetailResponse:
        """Get detailed project information with pages."""
        project = await self.get_project(project_id)
        pages = await self.get_project_pages(project_id)

        return ProjectDetailResponse(
            **project.model_dump(),
            pages=pages
        )

    async def create_project_page(
        self,
        project_id: str,
        page_data: ProjectPageCreate,
        file_path: str,
        file_size: int,
        image_width: Optional[int] = None,
        image_height: Optional[int] = None
    ) -> ProjectPageResponse:
        """Create a new project page."""
        # Check if project exists
        await self.get_project(project_id)

        # Create ORM model instance - timestamps will be set automatically
        page = ProjectPage(
            project_id=project_id,
            page_number=page_data.page_number,
            original_filename=page_data.original_filename,
            file_path=file_path,
            file_size=file_size,
            image_width=image_width,
            image_height=image_height,
            ocr_status=OCRStatus.PENDING
        )

        self.session.add(page)
        await self.session.commit()
        await self.session.refresh(page)

        return await self.get_project_page(page.id)

    async def get_project_page(self, page_id: str) -> ProjectPageResponse:
        """Get a project page by ID."""
        # Get the project page
        query = select(ProjectPage).where(ProjectPage.id == page_id)
        result = await self.session.execute(query)
        page = result.scalar_one_or_none()

        if not page:
            raise ProjectPageNotFound(
                f"Project page with ID {page_id} not found")

        # Get text region count
        text_region_count_query = select(func.count(
            TextRegion.id)).where(TextRegion.page_id == page_id)
        text_region_count_result = await self.session.execute(text_region_count_query)
        text_region_count = text_region_count_result.scalar() or 0

        # Create response with aggregated data
        page_dict = page.to_dict()
        page_dict["text_region_count"] = text_region_count
        return ProjectPageResponse(**page_dict)

    async def get_project_pages(self, project_id: str) -> List[ProjectPageResponse]:
        """Get all pages for a project."""
        # Get project pages
        query = select(ProjectPage).where(ProjectPage.project_id ==
                                          project_id).order_by(ProjectPage.page_number)
        result = await self.session.execute(query)
        pages = result.scalars().all()

        # Get text region counts for all pages in one query
        page_ids = [page.id for page in pages]
        if page_ids:
            text_region_count_query = select(
                TextRegion.page_id,
                func.count(TextRegion.id).label("text_region_count")
            ).where(TextRegion.page_id.in_(page_ids)).group_by(TextRegion.page_id)
            text_region_count_result = await self.session.execute(text_region_count_query)
            text_region_counts = {
                row.page_id: row.text_region_count for row in text_region_count_result}
        else:
            text_region_counts = {}

        # Build responses
        responses = []
        for page in pages:
            page_dict = page.to_dict()
            page_dict["text_region_count"] = text_region_counts.get(page.id, 0)
            responses.append(ProjectPageResponse(**page_dict))

        return responses

    async def get_project_page_detail(self, page_id: str) -> ProjectPageDetailResponse:
        """Get detailed project page information with text regions."""
        page = await self.get_project_page(page_id)
        text_regions = await self.get_page_text_regions(page_id)

        return ProjectPageDetailResponse(
            **page.model_dump(),
            text_regions=text_regions
        )

    async def delete_project_page(self, page_id: str) -> bool:
        """Delete a project page."""
        # Check if page exists
        await self.get_project_page(page_id)

        query = delete(ProjectPage).where(ProjectPage.id == page_id)
        await self.session.execute(query)
        await self.session.commit()
        return True

    async def create_text_region(self, page_id: str, region_data: TextRegionCreate) -> TextRegionResponse:
        """Create a new text region."""
        # Check if page exists
        await self.get_project_page(page_id)

        # Create ORM model instance - timestamps will be set automatically
        text_region = TextRegion(
            page_id=page_id,
            region_type=region_data.region_type,
            x=region_data.x,
            y=region_data.y,
            width=region_data.width,
            height=region_data.height,
            original_text=region_data.original_text,
            confidence_score=region_data.confidence_score,
            translation_status=TranslationStatus.PENDING
        )

        self.session.add(text_region)
        await self.session.commit()
        await self.session.refresh(text_region)

        return await self.get_text_region(text_region.id)

    async def get_text_region(self, region_id: str) -> TextRegionResponse:
        """Get a text region by ID."""
        query = select(TextRegion).where(TextRegion.id == region_id)
        result = await self.session.execute(query)
        text_region = result.scalar_one_or_none()

        if not text_region:
            raise TextRegionNotFound(
                f"Text region with ID {region_id} not found")

        return TextRegionResponse(**text_region.to_dict())

    async def get_page_text_regions(self, page_id: str) -> List[TextRegionResponse]:
        """Get all text regions for a page."""
        query = select(TextRegion).where(TextRegion.page_id ==
                                         page_id).order_by(TextRegion.created_at)
        result = await self.session.execute(query)
        text_regions = result.scalars().all()
        return [TextRegionResponse(**text_region.to_dict()) for text_region in text_regions]

    async def update_text_region(self, region_id: str, region_data: TextRegionUpdate) -> TextRegionResponse:
        """Update a text region."""
        # Check if region exists
        await self.get_text_region(region_id)

        # Build update data
        update_data = region_data.model_dump(exclude_unset=True)
        if not update_data:
            return await self.get_text_region(region_id)

        query = update(TextRegion).where(
            TextRegion.id == region_id).values(**update_data)
        await self.session.execute(query)
        await self.session.commit()

        return await self.get_text_region(region_id)

    async def delete_text_region(self, region_id: str) -> bool:
        """Delete a text region."""
        # Check if region exists
        await self.get_text_region(region_id)

        query = delete(TextRegion).where(TextRegion.id == region_id)
        await self.session.execute(query)
        await self.session.commit()
        return True

    async def update_page_ocr_status(self, page_id: str, status: OCRStatus) -> ProjectPageResponse:
        """Update OCR status for a page."""
        query = update(ProjectPage).where(
            ProjectPage.id == page_id).values(ocr_status=status)
        await self.session.execute(query)
        await self.session.commit()
        return await self.get_project_page(page_id)
