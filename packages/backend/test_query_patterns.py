#!/usr/bin/env python3
"""
Comprehensive test script to verify all service query patterns work correctly.
"""
import asyncio
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from src.projects.service import ProjectService
from src.ocr.service import OCRService
from src.translation.service import TranslationService
from src.projects.schemas import ProjectCreate, ProjectPageCreate, TextRegionCreate
from src.ocr.schemas import OCRJobCreate
from src.translation.schemas import TranslationJobCreate
from src.constants import ProjectStatus, OCRStatus, TranslationStatus, TextRegionType, LLMProvider


async def test_all_query_patterns():
    """Test that all service query patterns work correctly."""
    # Create in-memory SQLite database for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)
    
    # Import and create tables
    from src.models import BaseModel
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)
    
    # Create session
    async_session = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)
    
    async with async_session() as session:
        project_service = ProjectService(session)
        ocr_service = OCRService(session)
        translation_service = TranslationService(session)
        
        try:
            # Test Projects Service
            print("🧪 Testing Projects Service...")
            
            # Create project
            project_data = ProjectCreate(
                name="Test Project",
                description="Test Description",
                source_language="japanese",
                target_language="english"
            )
            project = await project_service.create_project(project_data)
            print(f"✅ Project created: {project.id}")
            
            # Get project (tests ORM approach with aggregated page_count)
            retrieved_project = await project_service.get_project(project.id)
            print(f"✅ Project retrieved: {retrieved_project.id}, page_count: {retrieved_project.page_count}")
            
            # Create project page
            page_data = ProjectPageCreate(
                page_number=1,
                original_filename="test.jpg"
            )
            page = await project_service.create_project_page(
                project.id, page_data, "/test/test.jpg", 1000, 800, 600
            )
            print(f"✅ Project page created: {page.id}")
            
            # Get project page (tests ORM approach with aggregated text_region_count)
            retrieved_page = await project_service.get_project_page(page.id)
            print(f"✅ Project page retrieved: {retrieved_page.id}, text_region_count: {retrieved_page.text_region_count}")
            
            # Create text region
            region_data = TextRegionCreate(
                region_type=TextRegionType.SPEECH_BUBBLE,
                x=0.1, y=0.2, width=0.3, height=0.4,
                original_text="Test text", confidence_score=0.95
            )
            text_region = await project_service.create_text_region(page.id, region_data)
            print(f"✅ Text region created: {text_region.id}")
            
            # Get text region (tests ORM approach)
            retrieved_region = await project_service.get_text_region(text_region.id)
            print(f"✅ Text region retrieved: {retrieved_region.id}")
            
            # Get page text regions (tests ORM approach)
            page_regions = await project_service.get_page_text_regions(page.id)
            print(f"✅ Page text regions retrieved: {len(page_regions)} regions")
            
            # Get projects (tests ORM approach with aggregated page_count)
            projects = await project_service.get_projects(limit=10)
            print(f"✅ Projects list retrieved: {len(projects)} projects")
            
            # Get project pages (tests ORM approach with aggregated text_region_count)
            pages = await project_service.get_project_pages(project.id)
            print(f"✅ Project pages retrieved: {len(pages)} pages")
            
            # Test OCR Service
            print("\n🧪 Testing OCR Service...")
            
            # Create OCR job
            ocr_job_data = OCRJobCreate(
                page_id=page.id,
                provider=LLMProvider.CLAUDE
            )
            ocr_job = await ocr_service.create_ocr_job(ocr_job_data)
            print(f"✅ OCR job created: {ocr_job.id}")
            
            # Get OCR job (tests ORM approach)
            retrieved_ocr_job = await ocr_service.get_ocr_job(ocr_job.id)
            print(f"✅ OCR job retrieved: {retrieved_ocr_job.id}")
            
            # Get OCR jobs by page (tests ORM approach)
            page_ocr_jobs = await ocr_service.get_ocr_jobs_by_page(page.id)
            print(f"✅ Page OCR jobs retrieved: {len(page_ocr_jobs)} jobs")
            
            # Test Translation Service
            print("\n🧪 Testing Translation Service...")
            
            # Create translation job
            translation_job_data = TranslationJobCreate(
                text_region_id=text_region.id,
                provider=LLMProvider.CLAUDE
            )
            translation_job = await translation_service.create_translation_job(translation_job_data)
            print(f"✅ Translation job created: {translation_job.id}")
            
            # Get translation job (tests ORM approach)
            retrieved_translation_job = await translation_service.get_translation_job(translation_job.id)
            print(f"✅ Translation job retrieved: {retrieved_translation_job.id}")
            
            # Get translation jobs by region (tests ORM approach)
            region_translation_jobs = await translation_service.get_translation_jobs_by_region(text_region.id)
            print(f"✅ Region translation jobs retrieved: {len(region_translation_jobs)} jobs")
            
            print("\n🎉 All query pattern tests passed! ORM standardization successful.")
            return True
            
        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    success = asyncio.run(test_all_query_patterns())
    sys.exit(0 if success else 1)
