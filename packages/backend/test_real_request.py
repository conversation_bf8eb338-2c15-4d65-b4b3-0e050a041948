#!/usr/bin/env python3
"""
Test script to make real HTTP requests to the running backend.
"""
import requests
import json


def test_real_text_region_update():
    """Test real HTTP requests to the running backend."""
    base_url = "http://localhost:8000"
    
    # Use the actual IDs from the error message
    project_id = "3e693b86-0507-492e-872c-844297fb4ce1"
    page_id = "1c912f92-1351-4108-9f81-43d55704bd8b"
    region_id = "c5b43981-7960-471b-b705-81cc348c6225"
    
    url = f"{base_url}/api/v1/projects/{project_id}/pages/{page_id}/regions/{region_id}"
    
    # Test various payloads that might be causing the 422 error
    test_payloads = [
        {
            "name": "Empty payload",
            "payload": {}
        },
        {
            "name": "Simple text update",
            "payload": {
                "original_text": "Updated text"
            }
        },
        {
            "name": "Position update",
            "payload": {
                "x": 0.5,
                "y": 0.6
            }
        },
        {
            "name": "Font styling",
            "payload": {
                "font_family": "Arial",
                "font_size": 14,
                "font_color": "#000000"
            }
        },
        {
            "name": "Common frontend payload",
            "payload": {
                "x": 0.5,
                "y": 0.6,
                "width": 0.2,
                "height": 0.3,
                "original_text": "Test text",
                "translated_text": "Translated text",
                "font_family": "Arial",
                "font_size": 12,
                "font_color": "#000000",
                "background_color": "#FFFFFF"
            }
        },
        {
            "name": "Potential problematic values",
            "payload": {
                "confidence_score": 0.95,
                "translation_status": "completed"
            }
        }
    ]
    
    print("🧪 Testing Real HTTP Requests to Backend...")
    print(f"Target URL: {url}")
    
    for test_case in test_payloads:
        print(f"\n🧪 Testing: {test_case['name']}")
        
        try:
            response = requests.put(
                url,
                json=test_case["payload"],
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            print(f"  Status: {response.status_code}")
            
            if response.status_code == 422:
                try:
                    error_detail = response.json()
                    print(f"  ❌ Validation Error:")
                    print(f"     {json.dumps(error_detail, indent=6)}")
                except:
                    print(f"  ❌ Validation Error (raw): {response.text}")
            elif response.status_code == 200:
                print(f"  ✅ Success")
                try:
                    result = response.json()
                    print(f"     Updated region ID: {result.get('id', 'unknown')}")
                except:
                    print(f"     Response: {response.text[:100]}...")
            elif response.status_code == 404:
                print(f"  ℹ️ Not Found (expected if data doesn't exist)")
            else:
                print(f"  ⚠️ Unexpected status: {response.status_code}")
                print(f"     Response: {response.text[:200]}...")
                
        except requests.exceptions.ConnectionError:
            print(f"  ❌ Connection Error - Backend not running on {base_url}")
            break
        except requests.exceptions.Timeout:
            print(f"  ❌ Request Timeout")
        except Exception as e:
            print(f"  ❌ Request Error: {e}")
    
    print("\n🎉 Real HTTP request testing completed!")


if __name__ == "__main__":
    test_real_text_region_update()
