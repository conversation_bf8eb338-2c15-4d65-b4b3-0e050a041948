#!/usr/bin/env python3
"""
Test script to reproduce and diagnose TextRegionUpdate validation errors.
"""
from pydantic import ValidationError
from src.constants import TextRegionType, TranslationStatus
from src.projects.schemas import ProjectCreate, ProjectPageCreate, TextRegionCreate, TextRegionUpdate
from src.projects.service import ProjectService
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import asyncio
import sys
from pathlib import Path

# Add the src directory to the path
sys.path.insert(0, str(Path(__file__).parent / "src"))


async def test_text_region_update_validation():
    """Test TextRegionUpdate validation to identify 422 error causes."""
    # Create in-memory SQLite database for testing
    engine = create_async_engine("sqlite+aiosqlite:///:memory:", echo=False)

    # Import and create tables
    from src.models import BaseModel
    async with engine.begin() as conn:
        await conn.run_sync(BaseModel.metadata.create_all)

    # Create session
    async_session = sessionmaker(
        engine, class_=AsyncSession, expire_on_commit=False)

    async with async_session() as session:
        service = ProjectService(session)

        try:
            print("🧪 Testing TextRegionUpdate Validation...")

            # Create test project and page
            project_data = ProjectCreate(
                name="Test Project",
                description="Test Description",
                source_language="japanese",
                target_language="english"
            )
            project = await service.create_project(project_data)

            page_data = ProjectPageCreate(
                page_number=1,
                original_filename="test.jpg"
            )
            page = await service.create_project_page(
                project.id, page_data, "/test/test.jpg", 1000, 800, 600
            )

            # Create initial text region
            region_data = TextRegionCreate(
                region_type=TextRegionType.SPEECH_BUBBLE,
                x=0.1, y=0.2, width=0.3, height=0.4,
                original_text="Original text", confidence_score=0.95
            )
            text_region = await service.create_text_region(page.id, region_data)
            print(f"✅ Created text region: {text_region.id}")

            # Test various update scenarios that might cause 422 errors
            test_cases = [
                {
                    "name": "Valid basic update",
                    "data": {
                        "original_text": "Updated text",
                        "translated_text": "Translated text"
                    }
                },
                {
                    "name": "Valid coordinate update",
                    "data": {
                        "x": 0.5,
                        "y": 0.6,
                        "width": 0.2,
                        "height": 0.3
                    }
                },
                {
                    "name": "Valid font styling",
                    "data": {
                        "font_family": "Arial",
                        "font_size": 12,
                        "font_color": "#000000",
                        "background_color": "#FFFFFF"
                    }
                },
                {
                    "name": "Edge case coordinates (0 and 1)",
                    "data": {
                        "x": 0.0,
                        "y": 1.0,
                        "width": 1.0,
                        "height": 0.1
                    }
                },
                {
                    "name": "Translation status update",
                    "data": {
                        "translation_status": TranslationStatus.COMPLETED
                    }
                },
                {
                    "name": "Invalid coordinate (negative)",
                    "data": {
                        "x": -0.1
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid coordinate (> 1)",
                    "data": {
                        "y": 1.1
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid width (0)",
                    "data": {
                        "width": 0.0
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid font size (too small)",
                    "data": {
                        "font_size": 7
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid font size (too large)",
                    "data": {
                        "font_size": 73
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid color format (no #)",
                    "data": {
                        "font_color": "000000"
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid color format (too short)",
                    "data": {
                        "font_color": "#000"
                    },
                    "should_fail": True
                },
                {
                    "name": "Invalid color format (invalid chars)",
                    "data": {
                        "font_color": "#GGGGGG"
                    },
                    "should_fail": True
                },
                {
                    "name": "Font family too long",
                    "data": {
                        "font_family": "A" * 101
                    },
                    "should_fail": True
                },
                {
                    "name": "Empty string values",
                    "data": {
                        "original_text": "",
                        "translated_text": "",
                        "font_family": ""
                    }
                },
                {
                    "name": "Null/None values",
                    "data": {
                        "original_text": None,
                        "translated_text": None,
                        "font_family": None,
                        "font_size": None,
                        "font_color": None
                    }
                },
                {
                    "name": "Float precision edge case",
                    "data": {
                        "x": 0.999999999999,
                        "y": 0.000000000001,
                        "width": 0.000000000001,
                        "height": 0.999999999999
                    }
                },
                {
                    "name": "Very small valid width/height",
                    "data": {
                        "width": 0.001,
                        "height": 0.001
                    }
                },
                {
                    "name": "Lowercase hex color",
                    "data": {
                        "font_color": "#abcdef",
                        "background_color": "#123456"
                    }
                },
                {
                    "name": "Mixed case hex color",
                    "data": {
                        "font_color": "#AbCdEf",
                        "background_color": "#123ABC"
                    }
                }
            ]

            for test_case in test_cases:
                print(f"\n🧪 Testing: {test_case['name']}")
                should_fail = test_case.get("should_fail", False)

                try:
                    # Test schema validation first
                    update_schema = TextRegionUpdate(**test_case["data"])
                    print(f"  ✅ Schema validation passed")

                    if not should_fail:
                        # Test service update
                        result = await service.update_text_region(text_region.id, update_schema)
                        print(f"  ✅ Service update passed: {result.id}")
                    else:
                        print(f"  ⚠️ Expected failure but schema validation passed")

                except ValidationError as e:
                    if should_fail:
                        print(
                            f"  ✅ Expected validation error: {e.errors()[0]['msg']}")
                    else:
                        print(f"  ❌ Unexpected validation error: {e.errors()}")

                except Exception as e:
                    print(f"  ❌ Service error: {e}")

            print("\n🎉 TextRegionUpdate validation testing completed!")
            return True

        except Exception as e:
            print(f"\n❌ Test failed: {e}")
            import traceback
            traceback.print_exc()
            return False


if __name__ == "__main__":
    success = asyncio.run(test_text_region_update_validation())
    sys.exit(0 if success else 1)
