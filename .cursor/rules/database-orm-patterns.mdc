---
description: Database ORM query patterns and consistency guidelines
globs: *.py, *.ts, *.js, *.go, *.rs, *.java, *.cs
alwaysApply: true
---

# Database ORM Query Patterns

## Consistency Rules

- Always use consistent query patterns across all service methods within a codebase
- Never mix raw SQL approaches with ORM approaches in the same service layer
- Always standardize on ORM model instances over explicit column selection for maintainability
- Always use the model's built-in serialization methods (to_dict, model_dump) when available

## Single Entity Retrieval

- Always use `scalar_one_or_none()` or equivalent for single entity queries
- Never use `first()` with model instance queries that return wrapped objects
- Always handle null cases explicitly before attempting to serialize

## Multiple Entity Retrieval

- Always use `scalars().all()` or equivalent for multiple entity queries
- Never use `fetchall()` with model instance queries that return wrapped objects
- Always iterate over actual model instances, not row mappings

## Aggregated Data Patterns

- Always use separate efficient queries for aggregated fields instead of complex joins when possible
- Always batch aggregation queries when processing multiple entities
- Never sacrifice query efficiency for code brevity in high-traffic methods

## Error Prevention

- Always verify query result types match expected serialization patterns
- Always test query pattern changes with comprehensive integration tests
- Never assume row mappings work the same way across different query types

## Refactoring Guidelines

- Always tackle service-by-service when standardizing query patterns
- Always preserve API contracts during internal query pattern changes
- Always run diagnostics after each query pattern modification
- Never declare refactoring complete without comprehensive testing
