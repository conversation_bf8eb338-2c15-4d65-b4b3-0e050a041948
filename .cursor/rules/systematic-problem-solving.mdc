---
description: Systematic approach to complex technical problems
globs: *
alwaysApply: true
---

# Systematic Problem Solving

## Task Management

- Always break complex multi-step work into discrete tasks with clear success criteria
- Always use task management tools for work that spans multiple files or services
- Always update task states efficiently using batch operations when possible
- Never proceed to next phase without completing current task verification

## Root Cause Analysis

- Always identify the underlying cause before implementing solutions
- Never fix symptoms without understanding the fundamental issue
- Always trace error patterns back to their architectural origins
- Always document the root cause analysis for future reference

## Incremental Approach

- Always tackle complex refactoring service-by-service or module-by-module
- Never attempt to change everything simultaneously
- Always verify each increment works before proceeding to the next
- Always maintain working state between incremental changes

## Comprehensive Testing

- Always create integration tests that exercise all modified functionality
- Never declare work complete without testing edge cases
- Always test the entire workflow, not just individual components
- Always verify that changes maintain existing API contracts

## Validation Workflow

- Always run diagnostics immediately after making code changes
- Always verify imports and syntax before testing functionality
- Never skip verification steps even for "simple" changes
- Always test both positive and negative scenarios

## Documentation and Communication

- Always explain the reasoning behind architectural decisions
- Never implement changes without documenting the approach
- Always provide clear success criteria for each phase of work
- Always summarize what was changed and why at completion
